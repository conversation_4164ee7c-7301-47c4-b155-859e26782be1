<template>
  <div class="chat-close-test">
    <el-card header="聊天关闭功能测试">
      <div class="test-section">
        <h3>测试说明</h3>
        <p>此页面用于测试聊天关闭功能是否正常工作</p>
        
        <div class="test-steps">
          <h4>测试步骤：</h4>
          <ol>
            <li>点击"开始聊天"按钮</li>
            <li>发送一些测试消息</li>
            <li>点击聊天窗口右上角的"X"关闭按钮</li>
            <li>确认是否弹出确认对话框</li>
            <li>选择"确定"关闭聊天</li>
            <li>检查后端日志确认是否调用了 endChatRoom 接口</li>
          </ol>
        </div>

        <div class="test-actions">
          <el-button type="primary" @click="startChat" :loading="testing">
            开始聊天测试
          </el-button>
          <el-button @click="checkChatStatus">
            检查聊天状态
          </el-button>
          <el-button @click="clearTestData">
            清理测试数据
          </el-button>
        </div>

        <div class="test-results" v-if="testResults.length > 0">
          <h4>测试结果：</h4>
          <div v-for="(result, index) in testResults" :key="index" 
               :class="['test-result-item', result.type]">
            <span class="result-time">{{ result.time }}</span>
            <span class="result-message">{{ result.message }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 聊天组件 -->
    <UserChat ref="userChat" />
  </div>
</template>

<script>
import UserChat from './UserChat.vue'

export default {
  name: 'ChatCloseTest',
  components: {
    UserChat
  },
  data() {
    return {
      testing: false,
      testResults: []
    }
  },
  methods: {
    /**
     * 开始聊天测试
     */
    async startChat() {
      this.testing = true
      this.addTestResult('info', '开始聊天测试...')
      
      try {
        // 调用 UserChat 组件的 startChat 方法
        if (this.$refs.userChat) {
          await this.$refs.userChat.startChat()
          this.addTestResult('success', '聊天启动成功，请进行关闭测试')
        } else {
          this.addTestResult('error', 'UserChat 组件未找到')
        }
      } catch (error) {
        this.addTestResult('error', '聊天启动失败: ' + error.message)
      } finally {
        this.testing = false
      }
    },

    /**
     * 检查聊天状态
     */
    checkChatStatus() {
      if (this.$refs.userChat) {
        const chatComponent = this.$refs.userChat
        const status = {
          isOpen: chatComponent.isOpen,
          roomCode: chatComponent.roomCode,
          isConnected: chatComponent.isConnected,
          messageCount: chatComponent.messages.length
        }
        
        this.addTestResult('info', `聊天状态: ${JSON.stringify(status, null, 2)}`)
      } else {
        this.addTestResult('error', 'UserChat 组件未找到')
      }
    },

    /**
     * 清理测试数据
     */
    clearTestData() {
      this.testResults = []
      this.addTestResult('info', '测试数据已清理')
    },

    /**
     * 添加测试结果
     */
    addTestResult(type, message) {
      this.testResults.push({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
    }
  }
}
</script>

<style scoped>
.chat-close-test {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
}

.test-steps {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-steps ol {
  margin: 10px 0;
  padding-left: 20px;
}

.test-actions {
  margin: 20px 0;
}

.test-actions .el-button {
  margin-right: 10px;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.test-result-item {
  display: flex;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
}

.test-result-item.success {
  background-color: #f0f9ff;
  color: #067f23;
}

.test-result-item.error {
  background-color: #fef2f2;
  color: #dc2626;
}

.test-result-item.info {
  background-color: #f8fafc;
  color: #374151;
}

.result-time {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
}

.result-message {
  flex: 1;
  white-space: pre-wrap;
}
</style>
