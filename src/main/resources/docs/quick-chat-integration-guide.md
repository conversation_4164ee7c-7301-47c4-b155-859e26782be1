# 快速聊天系统集成指南

## 📋 概述

本文档基于"快速开始聊天"功能，提供最简化的前后端集成方案。用户一键启动聊天，客服通过工作台处理用户消息。

## 用户端与客服端界面

### 用户端—在线客服按钮

![image-20250628172655867](quick-chat-integration-guide/image-20250628172655867.png)

### 点击按钮弹出聊天界面

![image-20250628172735318](quick-chat-integration-guide/image-20250628172735318.png)



### 客服端—客服工作台

暂无界面

## 🎯 核心功能流程

> 用户登录后调用/api/user/getInfo接口获取用户信息，根据返回的roleName，若roleName包含root则属于客服，否则属于用户。
>
> 用户端则显示“在线客服”按钮；客服端则显示客服工作台

### 用户端流程
1. 用户登录获取JWT Token
2. 用户点击"在线客服"按钮弹出聊天窗口
3. 连接websocket
4. 调用 /api/chat/quick-start 接口
   - 创建或获取聊天室
   - 自动分配客服（如果有可用客服）
   - 返回：
     - roomCode（房间编码）
     - roomId（房间 ID）
     - customerServiceAssigned（是否成功分配客服）
     - recentMessages（最近消息）
5. 订阅聊天室
   - 根据返回的 roomCode 调用 subscribeToRoom 方法订阅聊天室消息。
   - 目的：确保能够接收该聊天室的广播消息。
6. 根据返回的 roomCode 调用 joinRoom 加入聊天室
7. 发送消息时调用 sendMessage
8. 结束聊天室调用/api/chat/end/{{roomCode}}接口

### 客服端流程

1. 客服登录，获取JWT Token → 打开客服工作台
2. 连接 WebSocket
3. 调用 /api/chat/service/pending-rooms 获取待处理聊天室列表;调用/api/chat/service/all-rooms?page=1&size=20获取所有聊天室列表
4. 点击某个聊天室后：
   - 订阅聊天室
     - 根据聊天室的 roomCode 调用 subscribeToRoom 方法订阅聊天室消息。
     - 目的：确保能够接收该聊天室的广播消息。
   - 调用 joinRoom 加入聊天室
     - 调用 /api/chat/service/take-over/{roomCode} 接管聊天室并获取详情
5. 回复用户消息时调用 sendMessage



## 🔑 核心接口

### 用户登录

```http
POST /api/user/login
Content-Type: application/json

{
  "userName": "用户名",
  "password": "密码"
}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```



### 获取用户信息

```http
GET	/api/user/getInfo
Authorization: {JWT_TOKEN}

Response:
{
    "code": "200",
    "message": "操作成功",
    "data": {
        "id": 1,
        "userName": "admin",
        "nickName": "gly",
        "password": null,
        "status": 1,
        "email": "<EMAIL>",
        "phonenumber": "13800001111",
        "sex": "1",
        "avatar": "http://example.com/avatar1.jpg",
        "createTime": "2025-06-11T16:00:00.000+00:00",
        "updateTime": "2025-06-23T16:00:00.000+00:00",
        "isDeleted": 0,
        "roleName": [
            "USER",
            "ROOT"
        ]
    }
}
```

### 快速开始聊天（用户端核心接口）

```json
POST /api/chat/quick-start
Authorization: {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roomCode": "ROOM_1735123456789",
    "roomId": 1,
    "status": 2,
    //"customerServiceAssigned": true,
    "recentMessages": [
      {
        "id": 1,
        "senderId": 1,
        "senderType": 1,
        "content": "你好，我需要帮助",
        "createTime": "2025-06-25T10:30:00"
      }
    ]
  }
}
```

### 获取待处理消息列表（客服端核心接口）

```http
GET /api/chat/service/pending-rooms
Authorization: {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "roomId": 1,
      "roomCode": "ROOM_1735123456789",
      "userId": 2,
      "userName": "张三",
      "userAvatar": "avatar_url",
      "latestMessage": "你好，我需要帮助",
      "latestMessageTime": "2025-06-25T10:30:00",
      "unreadCount": 3,
      "createTime": "2025-06-25T10:25:00"
    }
  ]
}
```

### 获取所有的聊天室列表（客服端核心接口）

```http
GET /api/chat/service/all-rooms?page=1&size=20
Authorization: {JWT_TOKEN}

Response:
{
    "code": "200",
    "message": "操作成功",
    "data": [
        {
            "createTime": "2025-06-29 14:51:10",
            "userAvatar": "http://example.com/avatar3.jpg",
            "latestMessageTime": "2025-06-29 17:26:15",
            "unreadCount": 0,
            "lastMessageTime": "2025-06-29 17:26:16",
            "latestMessage": "cess",
            "roomCode": "ROOM_1939215046022926336",
            "userName": "老王",
            "userId": 3,
            "roomId": 3,
            "status": 0,
            "latestMessageType": 1
        }
    ]
}
```



### 客服接管聊天室

```http
POST /api/chat/service/take-over/{roomCode}
Authorization: {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roomCode": "ROOM_1735123456789",
    "roomId": 1,
    "userId": 2,
    "messages": [
      {
        "id": 1,
        "senderId": 2,
        "senderType": 1,
        "content": "你好，我需要帮助",
        "createTime": "2025-06-25T10:30:00"
      }
    ]
  }
}
```



### 获取聊天历史

```http
GET /api/chat/history/{roomCode}?page=1&size=20
Authorization:{token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "messages": [
      {
        "id": 1,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "你好，我需要帮助",
        "isRead": 1,
        "createTime": "2025-06-25T10:31:00"
      }
    ],
    "roomInfo": {
      "id": 1,
      "roomCode": "ROOM_1735123456789",
      "status": 1
    },
    "page": 1,
    "size": 20
  }
}
```

**消息类型说明**：

- `senderType`: 1-用户，2-客服
- `messageType`: 1-文本，2-图片，3-文件
- `isRead`: 0-未读，1-已读

### 结束聊天室

```http
POST /api/chat/end/{{roomCode}}
Authorization:{token}

Response:
{"code":"200","message":"操作成功","data":"聊天室已结束"}
```



## 🔌 WebSocket通信

### 连接信息
```
端点: ws://localhost:8080/ws/chat
协议: STOMP over SockJS
认证: Headers中携带Authorization: {JWT_TOKEN}
```

### 核心WebSocket方法

#### 1. 订阅聊天室消息
```javascript
stompClient.subscribe(`/topic/chat/${roomCode}`, (message) => {
  const data = JSON.parse(message.body)
  console.log('收到消息:', data)
})
```

#### 2. 加入聊天室
```javascript
stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))
```

#### 3. 发送消息
```javascript
stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
  content: "消息内容"
}))
```

## 💻 前端实现示例

### 用户端实现
```javascript
class UserChatWidget {
  async startChat() {
    try {
      // 1. 快速启动聊天
      const token = localStorage.getItem('token')
      const response = await fetch('/api/chat/quick-start', {
        method: 'POST',
        headers: { 'Authorization': token }
      })
      const result = await response.json()
      
      this.roomCode = result.data.roomCode
      this.messages = result.data.recentMessages || []
      
      // 2. 连接WebSocket
      await this.connectWebSocket()
      
      // 3. 显示聊天界面
      this.showChatWindow()
      
    } catch (error) {
      console.error('启动聊天失败:', error)
    }
  }

  async connectWebSocket() {
    const token = localStorage.getItem('token')
    const socket = new SockJS('/ws/chat')
    this.stompClient = Stomp.over(socket)
    
    return new Promise((resolve, reject) => {
      this.stompClient.connect(
        { 'Authorization': token },
        () => {
          // 自动订阅和加入
          this.stompClient.subscribe(`/topic/chat/${this.roomCode}`, (message) => {
            const data = JSON.parse(message.body)
            if (data.type !== 'USER_JOINED') {
              this.messages.push(data)
              this.updateChatDisplay()
            }
          })
          
          this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))
          resolve()
        },
        reject
      )
    })
  }

  sendMessage(content) {
    if (!content.trim()) return
    
    this.stompClient.send(`/app/chat.sendMessage/${this.roomCode}`, {}, JSON.stringify({
      content: content
    }))
  }
}
```

### 客服端实现
```javascript
class CustomerServiceWorkbench {
  async init() {
    // 1. 获取待处理消息列表
    await this.loadPendingRooms()
    
    // 2. 连接WebSocket
    await this.connectWebSocket()
    
    // 3. 定时刷新
    setInterval(() => this.loadPendingRooms(), 10000)
  }

  async loadPendingRooms() {
    const token = localStorage.getItem('token')
    const response = await fetch('/api/chat/service/pending-rooms', {
      headers: { 'Authorization': token }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      this.pendingRooms = result.data
      this.updatePendingList()
    }
  }

  async takeOverRoom(room) {
    const token = localStorage.getItem('token')
    const response = await fetch(`/api/chat/service/take-over/${room.roomCode}`, {
      method: 'POST',
      headers: { 'Authorization': token }
    })
    const result = await response.json()
    
    if (result.code === 200) {
      this.currentRoom = room
      this.currentMessages = result.data.messages || []
      
      // 订阅聊天室
      this.stompClient.subscribe(`/topic/chat/${room.roomCode}`, (message) => {
        const data = JSON.parse(message.body)
        if (data.type !== 'USER_JOINED') {
          this.currentMessages.push(data)
          this.updateChatDisplay()
        }
      })
      
      // 加入聊天室
      this.stompClient.send(`/app/chat.joinRoom/${room.roomCode}`, {}, JSON.stringify({}))
      
      // 从待处理列表移除
      this.pendingRooms = this.pendingRooms.filter(r => r.roomId !== room.roomId)
      this.updatePendingList()
    }
  }

  sendMessage(content) {
    if (!content.trim() || !this.currentRoom) return
    
    this.stompClient.send(`/app/chat.sendMessage/${this.currentRoom.roomCode}`, {}, JSON.stringify({
      content: content
    }))
  }
}
```

## 🧪 快速测试

### 1. 测试用户聊天
```bash
# 1. 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"userName": "testuser", "password": "password123"}'

# 2. 快速开始聊天
curl -X POST http://localhost:8080/api/chat/quick-start \
  -H "Authorization: {USER_TOKEN}"
```

### 2. 测试客服工作台
```bash
# 1. 客服登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"userName": "service1", "password": "password123"}'

# 2. 获取待处理消息
curl -X GET http://localhost:8080/api/chat/service/pending-rooms \
  -H "Authorization: {SERVICE_TOKEN}"

# 3. 接管聊天室
curl -X POST http://localhost:8080/api/chat/service/take-over/ROOM_1735123456789 \
  -H "Authorization: {SERVICE_TOKEN}"
```

## 📊 消息格式说明

### WebSocket消息格式
```javascript
// 用户/客服发送的消息
{
  "id": 1,
  "roomCode": "ROOM_1735123456789",
  "senderId": 1,
  "senderName": "张三",
  "senderType": 1,  // 1=用户, 2=客服
  "messageType": 1, // 1=文本消息
  "content": "消息内容",
  "createTime": "2025-06-25T10:30:00"
}

// 用户加入通知
{
  "type": "USER_JOINED",
  "userId": 1,
  "userName": "张三",
  "message": "张三 加入了聊天"
}
```

## ⚠️ 注意事项

### 1. Token认证
- 所有API请求都需要在Header中携带JWT Token
- Token格式：`Authorization: {JWT_TOKEN}`（不需要Bearer前缀）

### 2. 权限控制
- 普通用户只能调用快速开始接口
- 客服用户才能调用客服工作台相关接口
- WebSocket连接需要验证用户身份

### 3. 错误处理
```javascript
// 统一错误响应格式
{
  "code": 403,
  "message": "非客服用户无权限访问"
}
```

### 4. 状态说明
- 聊天室状态：0=关闭, 1=活跃, 2=等待客服
- 发送者类型：1=用户, 2=客服
- 消息类型：1=文本消息

## 🎯 集成检查清单

### 用户端
- [ ] 实现用户登录获取Token
- [ ] 实现快速开始聊天接口调用
- [ ] 实现WebSocket连接和消息处理
- [ ] 实现聊天界面显示和交互

### 客服端
- [ ] 实现客服登录和权限验证
- [ ] 实现待处理消息列表显示
- [ ] 实现聊天室接管功能
- [ ] 实现客服聊天界面和消息发送

### 通用
- [ ] WebSocket连接稳定性测试
- [ ] 消息实时性测试
- [ ] 错误处理和重连机制
- [ ] 权限验证测试

完成以上功能即可实现基本的用户-客服聊天系统。
