package com.yqs.springbootminio.constants;

/**
 * 聊天系统常量类
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
public class ChatConstants {

    /**
     * 聊天室状态常量
     */
    public static class RoomStatus {
        /** 关闭 */
        public static final Integer CLOSED = 0;
        /** 活跃 */
        public static final Integer ACTIVE = 1;
        /** 等待客服 */
        public static final Integer WAITING_SERVICE = 2;
    }

    /**
     * 发送者类型常量
     */
    public static class SenderType {
        /** 用户 */
        public static final Integer USER = 1;
        /** 客服 */
        public static final Integer CUSTOMER_SERVICE = 2;
    }

    /**
     * 消息类型常量
     */
    public static class MessageType {
        /** 文本消息 */
        public static final Integer TEXT = 1;
        /** 图片消息 */
        public static final Integer IMAGE = 2;
        /** 文件消息 */
        public static final Integer FILE = 3;
    }

    /**
     * 消息读取状态常量
     */
    public static class ReadStatus {
        /** 未读 */
        public static final Integer UNREAD = 0;
        /** 已读 */
        public static final Integer READ = 1;
    }

    /**
     * 房间编码前缀
     */
    public static final String ROOM_CODE_PREFIX = "ROOM_";

    /**
     * 默认分页大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;

    /**
     * 最大分页大小
     */
    public static final Integer MAX_PAGE_SIZE = 100;

    /**
     * 私有构造函数，防止实例化
     */
    private ChatConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
