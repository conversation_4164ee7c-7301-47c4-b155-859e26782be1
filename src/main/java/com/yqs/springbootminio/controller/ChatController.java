package com.yqs.springbootminio.controller;


import com.yqs.springbootminio.model.AdminUserDetails;
import com.yqs.springbootminio.model.ChatMessage;
import com.yqs.springbootminio.model.ChatRoom;
import com.yqs.springbootminio.model.CustomerService;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.ChatService;
import com.yqs.springbootminio.service.CustomerServiceService;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
@Api(tags = "聊天管理", description = "提供聊天相关的操作")
public class ChatController {

    @Autowired
    private ChatService chatService;

    @Autowired
    private CustomerServiceService customerServiceService;

    /**
     * 创建或获取聊天室
     */
    @PostMapping("/room")
    @ApiOperation("创建或获取聊天室")
    public CommonResult<ChatRoom> createOrGetChatRoom() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            ChatRoom chatRoom = chatService.createOrGetChatRoom(currentUser.getId());
            return CommonResult.success(chatRoom);

        } catch (Exception e) {
            log.error("创建或获取聊天室失败", e);
            return CommonResult.failed("500", "创建或获取聊天室失败：" + e.getMessage());
        }
    }

    /**
     * 快速开始聊天（一键创建聊天室并分配客服）
     */
    @PostMapping("/quick-start")
    @ApiOperation("快速开始聊天")
    public CommonResult<Map<String, Object>> quickStartChat() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 1. 创建或获取聊天室
            ChatRoom chatRoom = chatService.createOrGetChatRoom(currentUser.getId());

            // 2. 自动分配客服
            boolean assignSuccess = chatService.autoAssignCustomerService(chatRoom.getId());

            Map<String, Object> result = new HashMap<>();
            result.put("roomCode", chatRoom.getRoomCode());
            result.put("roomId", chatRoom.getId());
            result.put("status", chatRoom.getStatus());
            result.put("customerServiceAssigned", assignSuccess);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("快速开始聊天失败", e);
            return CommonResult.failed("500", "快速开始聊天失败：" + e.getMessage());
        }
    }

    @GetMapping("/service/all-rooms")
    @ApiOperation("获取所有的聊天室列表")
    public CommonResult<List<Map<String, Object>>> getAllChatRooms(@ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer page,
                                                                   @ApiParam(value = "每页大小", defaultValue = "20") @RequestParam(defaultValue = "20") Integer size) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("❌ 用户未登录");
                return CommonResult.failed("401", "用户未登录");
            }
            boolean isCustomerService = customerServiceService.isCustomerService(currentUser.getId());
            if (!isCustomerService) {
                log.warn("❌ 非客服用户无权限访问: UserId={}", currentUser.getId());
                return CommonResult.failed("403", "非客服用户无权限访问");
            }
            List<Map<String, Object>> allChatRooms = chatService.getAllChatRooms(page, size, currentUser.getId());
            return CommonResult.success(allChatRooms);
        } catch (Exception e) {
            log.error("获取所有的聊天室列表失败", e);
            return CommonResult.failed("500", "获取所有聊天室列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取等待客服的聊天室列表（客服专用）
     */
    @GetMapping("/service/pending-rooms")
    @ApiOperation("获取等待客服的聊天室列表")
    public CommonResult<List<Map<String, Object>>> getPendingChatRooms() {
        try {
            log.info("🎯 开始处理获取等待客服的聊天室列表请求...");

            User currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("❌ 用户未登录");
                return CommonResult.failed("401", "用户未登录");
            }
            log.info("✅ 当前用户: ID={}, UserName={}", currentUser.getId(), currentUser.getUserName());

            // 检查是否为客服
            boolean isCustomerService = customerServiceService.isCustomerService(currentUser.getId());
//            log.info("🔍 客服权限检查结果: {}", isCustomerService);
            if (!isCustomerService) {
                log.warn("❌ 非客服用户无权限访问: UserId={}", currentUser.getId());
                return CommonResult.failed("403", "非客服用户无权限访问");
            }

            List<Map<String, Object>> pendingRooms = chatService.getPendingChatRooms(currentUser.getId());
//            log.info("📋 获取到的待处理聊天室数量: {}", pendingRooms != null ? pendingRooms.size() : 0);
//            log.info("📋 待处理聊天室详细数据: {}", pendingRooms);

            CommonResult<List<Map<String, Object>>> result = CommonResult.success(pendingRooms);
//            log.info("✅ 返回结果: {}", result);

            return result;

        } catch (Exception e) {
            log.error("❌ 获取等待客服的聊天室列表失败", e);
            return CommonResult.failed("500", "获取等待客服的聊天室列表失败：" + e.getMessage());
        }
    }

    /**
     * 客服接管聊天室
     */
    @PostMapping("/service/take-over/{roomCode}")
    @ApiOperation("客服接管聊天室")
    public CommonResult<Map<String, Object>> takeOverChatRoom(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 检查是否为客服
            if (!customerServiceService.isCustomerService(currentUser.getId())) {
                return CommonResult.failed("403", "非客服用户无权限访问");
            }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            chatService.assignCustomerService(chatRoom.getId(), currentUser.getId());

            // 获取聊天历史
            List<ChatMessage> messages = chatService.getChatHistory(chatRoom.getId(), 1, 50);

            Map<String, Object> result = new HashMap<>();
            result.put("roomCode", roomCode);
            result.put("roomId", chatRoom.getId());
            result.put("userId", chatRoom.getUserId());
            result.put("messages", messages);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("客服接管聊天室失败", e);
            return CommonResult.failed("500", "客服接管聊天室失败：" + e.getMessage());
        }
    }

    /**
     * 获取聊天历史
     */
    @GetMapping("/history/{roomCode}")
    @ApiOperation("获取聊天历史")
    public CommonResult<Map<String, Object>> getChatHistory(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "每页大小", defaultValue = "20") @RequestParam(defaultValue = "20") Integer size) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            // 验证权限
            if (!chatRoom.getUserId().equals(currentUser.getId()) &&
                    !isCustomerService(currentUser.getId(), chatRoom.getCustomerServiceId())) {
                return CommonResult.failed("403", "无权限访问此聊天室");
            }

            // 获取聊天历史
            List<ChatMessage> messages = chatService.getChatHistory(chatRoom.getId(), page, size);

            // 标记消息为已读
            chatService.markMessagesAsRead(chatRoom.getId(), currentUser.getId());

            Map<String, Object> result = new HashMap<>();
            result.put("messages", messages);
            result.put("roomInfo", chatRoom);
            result.put("page", page);
            result.put("size", size);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("获取聊天历史失败", e);
            return CommonResult.failed("500", "获取聊天历史失败：" + e.getMessage());
        }
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread/{roomCode}")
    @ApiOperation("获取未读消息数量")
    public CommonResult<Integer> getUnreadMessageCount(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            // 验证权限
            if (!chatRoom.getUserId().equals(currentUser.getId()) &&
                    !isCustomerService(currentUser.getId(), chatRoom.getCustomerServiceId())) {
                return CommonResult.failed("403", "无权限访问此聊天室");
            }

            int unreadCount = chatService.getUnreadMessageCount(chatRoom.getId(), currentUser.getId());
            return CommonResult.success(unreadCount);

        } catch (Exception e) {
            log.error("获取未读消息数量失败", e);
            return CommonResult.failed("500", "获取未读消息数量失败：" + e.getMessage());
        }
    }

    /**
     * 结束聊天室
     */
    @PostMapping("/end/{roomCode}")
    @ApiOperation("结束聊天室")
    public CommonResult<String> endChatRoom(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            // 验证权限（用户或客服都可以结束聊天）
            if (!customerServiceService.hasRoomPermission(currentUser.getId(),
                    chatRoom.getUserId(), chatRoom.getCustomerServiceId())) {
                return CommonResult.failed("403", "无权限执行此操作");
            }

            chatService.endChatRoom(chatRoom.getId());
            return CommonResult.success("聊天室已结束");

        } catch (Exception e) {
            log.error("结束聊天室失败", e);
            return CommonResult.failed("500", "结束聊天室失败：" + e.getMessage());
        }
    }



    /**
     * 获取用户聊天室列表
     */
    @GetMapping("/rooms")
    @ApiOperation("获取用户聊天室列表")
    public CommonResult<List<ChatRoom>> getUserChatRooms() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            List<ChatRoom> chatRooms = chatService.getUserChatRooms(currentUser.getId());
            return CommonResult.success(chatRooms);

        } catch (Exception e) {
            log.error("获取用户聊天室列表失败", e);
            return CommonResult.failed("500", "获取用户聊天室列表失败：" + e.getMessage());
        }
    }



    /**
     * 分配客服（仅管理员可用）
     */
    @PostMapping("/assign-service/{roomCode}")
    @ApiOperation("分配客服")
    public CommonResult<String> assignCustomerService(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode,
            @ApiParam(value = "客服用户ID", required = true) @RequestParam Long customerServiceId) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 这里可以添加管理员权限验证
            // if (!isAdmin(currentUser)) {
            //     return CommonResult.failed("403", "无权限执行此操作");
            // }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            chatService.assignCustomerService(chatRoom.getId(), customerServiceId);
            return CommonResult.success("客服分配成功");

        } catch (Exception e) {
            log.error("分配客服失败", e);
            return CommonResult.failed("500", "分配客服失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (!(principal instanceof AdminUserDetails)) {
            return null;
        }

        AdminUserDetails userDetails = (AdminUserDetails) principal;
        return userDetails.getAdminUser();
    }

    /**
     * 获取客服聊天室列表
     */
    @GetMapping("/service/rooms")
    @ApiOperation("获取客服聊天室列表")
    public CommonResult<List<ChatRoom>> getCustomerServiceChatRooms() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 检查是否为客服
            if (!customerServiceService.isCustomerService(currentUser.getId())) {
                return CommonResult.failed("403", "非客服用户无权限访问");
            }

            List<ChatRoom> chatRooms = chatService.getCustomerServiceChatRooms(currentUser.getId());
            return CommonResult.success(chatRooms);

        } catch (Exception e) {
            log.error("获取客服聊天室列表失败", e);
            return CommonResult.failed("500", "获取客服聊天室列表失败：" + e.getMessage());
        }
    }

    /**
     * 自动分配客服
     */
    @PostMapping("/auto-assign/{roomCode}")
    @ApiOperation("自动分配客服")
    public CommonResult<String> autoAssignCustomerService(
            @ApiParam(value = "房间编码", required = true) @PathVariable String roomCode) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                return CommonResult.failed("404", "聊天室不存在");
            }

            // 只有聊天室的用户可以请求自动分配客服
            if (!chatRoom.getUserId().equals(currentUser.getId())) {
                return CommonResult.failed("403", "无权限执行此操作");
            }

            boolean success = chatService.autoAssignCustomerService(chatRoom.getId());
            if (success) {
                return CommonResult.success("客服自动分配成功");
            } else {
                return CommonResult.failed("500", "暂无可用客服，请稍后再试");
            }

        } catch (Exception e) {
            log.error("自动分配客服失败", e);
            return CommonResult.failed("500", "自动分配客服失败：" + e.getMessage());
        }
    }



    /**
     * 获取在线客服列表
     */
    @GetMapping("/services/online")
    @ApiOperation("获取在线客服列表")
    public CommonResult<List<CustomerService>> getOnlineServices() {
        try {
            List<CustomerService> onlineServices = customerServiceService.getOnlineServices();
            return CommonResult.success(onlineServices);

        } catch (Exception e) {
            log.error("获取在线客服列表失败", e);
            return CommonResult.failed("500", "获取在线客服列表失败：" + e.getMessage());
        }
    }


    /**
     * 检查是否为客服
     */
    private boolean isCustomerService(Long userId, Long customerServiceId) {
        return customerServiceId != null && customerServiceId.equals(userId);
    }
}
