package com.yqs.springbootminio.service.impl;

import cn.hutool.core.util.IdUtil;
import com.yqs.springbootminio.dao.ChatMessageDO;
import com.yqs.springbootminio.dao.ChatRoomDO;
import com.yqs.springbootminio.dao.UserDO;
import com.yqs.springbootminio.enums.ChatRoomStatusEnum;
import com.yqs.springbootminio.enums.SenderTypeEnum;
import com.yqs.springbootminio.exception.BizException;
import com.yqs.springbootminio.mapper.ChatMessageMapper;
import com.yqs.springbootminio.mapper.ChatRoomMapper;
import com.yqs.springbootminio.mapper.UserMapper;
import com.yqs.springbootminio.model.ChatMessage;
import com.yqs.springbootminio.model.ChatRoom;
import com.yqs.springbootminio.model.CustomerService;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.ChatService;
import com.yqs.springbootminio.service.CustomerServiceService;
import com.yqs.springbootminio.service.UserService;
import com.yqs.springbootminio.util.CopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Autowired
    private ChatRoomMapper chatRoomMapper;

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CustomerServiceService customerServiceService;

    @Autowired
    private UserService userService;

    @Autowired
    private ChatServiceImpl chatService;

    @Override
    @Transactional
    public ChatRoom createOrGetChatRoom(Long userId) {
        // 先查找用户是否有活跃的聊天室
        ChatRoomDO existingRoom = chatRoomMapper.selectActiveRoomByUserId(userId);
        if (existingRoom != null) {
            return CopyUtil.copy(existingRoom, ChatRoom.class);
        }

        // 创建新的聊天室
        ChatRoomDO chatRoomDO = new ChatRoomDO();
        chatRoomDO.setRoomCode(generateRoomCode());
        chatRoomDO.setUserId(userId);
        chatRoomDO.setStatus(ChatRoomStatusEnum.WAITING_SERVICE.getStatus());
        chatRoomDO.setCreateTime(new Date());
        chatRoomDO.setUpdateTime(new Date());

        chatRoomMapper.insert(chatRoomDO);
        log.info("创建新聊天室，用户ID: {}, 房间编码: {}", userId, chatRoomDO.getRoomCode());

        return CopyUtil.copy(chatRoomDO, ChatRoom.class);
    }

    @Override
    @Transactional
    public ChatMessage sendMessage(Long roomId, Long senderId, Integer senderType, Integer messageType, String content) {
        // 保存消息
        ChatMessageDO messageDO = new ChatMessageDO();
        messageDO.setRoomId(roomId);
        messageDO.setSenderId(senderId);
        messageDO.setSenderType(senderType);
        messageDO.setMessageType(messageType);
        messageDO.setContent(content);
        messageDO.setIsRead(0);
        messageDO.setCreateTime(new Date());

        chatMessageMapper.insert(messageDO);

        // 更新聊天室最后消息时间
        ChatRoomDO roomDO = new ChatRoomDO();
        roomDO.setId(roomId);
        roomDO.setLastMessageTime(new Date());
        // 如果是用户发送消息且聊天室状态是等待客服，则保持等待状态
        // 如果是客服发送消息，则将状态改为活跃
        if (SenderTypeEnum.CUSTOMER_SERVICE.getType().equals(senderType)) {
            roomDO.setStatus(ChatRoomStatusEnum.ACTIVE.getStatus());
        }
        chatRoomMapper.update(roomDO);

        log.info("发送消息成功，房间ID: {}, 发送者ID: {}, 消息类型: {}", roomId, senderId, senderType);

        return CopyUtil.copy(messageDO, ChatMessage.class);
    }

    @Override
    public List<ChatMessage> getChatHistory(Long roomId, Integer page, Integer size) {
        int offset = (page - 1) * size;
        List<ChatMessageDO> messageDOList = chatMessageMapper.selectByRoomId(roomId, offset, size);
        List<ChatMessage> messageList = new ArrayList<>();
        for (ChatMessageDO chatMessageDO : messageDOList) {
            ChatMessage chatMessage = CopyUtil.copy(chatMessageDO, ChatMessage.class);
            UserDO userDO = userMapper.selectById(chatMessageDO.getSenderId());
            chatMessage.setSender(CopyUtil.copy(userDO, User.class));
            messageList.add(chatMessage);
        }
        return messageList;
    }

    @Override
    public List<ChatRoom> getUserChatRooms(Long userId) {
        List<ChatRoomDO> roomDOList = chatRoomMapper.selectByUserId(userId);
        return roomDOList.stream()
                .map(roomDO -> CopyUtil.copy(roomDO, ChatRoom.class))
                .collect(Collectors.toList());
    }

    @Override
    public ChatRoom getChatRoomByCode(String roomCode) {
        ChatRoomDO roomDO = chatRoomMapper.selectByRoomCode(roomCode);
        return roomDO != null ? CopyUtil.copy(roomDO, ChatRoom.class) : null;
    }

    @Override
    @Transactional
    public void markMessagesAsRead(Long roomId, Long readerId) {
        chatMessageMapper.markAsRead(roomId, readerId);
        log.info("标记消息为已读，房间ID: {}, 阅读者ID: {}", roomId, readerId);
    }

    @Override
    public int getUnreadMessageCount(Long roomId, Long readerId) {
        return chatMessageMapper.countUnreadMessages(roomId, readerId);
    }

    @Override
    @Transactional
    public void assignCustomerService(Long roomId, Long customerServiceId) {
        // 获取聊天室信息
        ChatRoomDO roomDO = chatRoomMapper.selectById(roomId);
        if (roomDO == null) {
            throw new BizException("聊天室不存在");
        }

        // 如果之前有客服，减少其聊天数
        if (roomDO.getCustomerServiceId() != null) {
            customerServiceService.decrementChatCount(roomDO.getCustomerServiceId());
        }

        // 分配新客服
        chatRoomMapper.assignCustomerService(roomId, customerServiceId);

        // 增加新客服的聊天数
        customerServiceService.incrementChatCount(customerServiceId);

        log.info("分配客服成功，房间ID: {}, 客服ID: {}", roomId, customerServiceId);
    }

    @Override
    @Transactional
    public boolean autoAssignCustomerService(Long roomId) {
        CustomerService availableService = customerServiceService.autoAssignService();
        if (availableService == null) {
            log.warn("没有可用的客服进行自动分配，房间ID: {}", roomId);
            return false;
        }

        chatService.assignCustomerService(roomId, availableService.getUserId());
        return true;
    }

    @Override
    public List<ChatRoom> getCustomerServiceChatRooms(Long customerServiceId) {
        List<ChatRoomDO> roomDOList = chatRoomMapper.selectByCustomerServiceId(customerServiceId);
        return roomDOList.stream()
                .map(roomDO -> CopyUtil.copy(roomDO, ChatRoom.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getPendingChatRooms() {
        try {
            log.info("🔍 开始获取等待客服的聊天室列表...");

            // 获取等待客服的聊天室
            List<ChatRoomDO> pendingRooms = chatRoomMapper.selectPendingRooms();

            List<Map<String, Object>> result = new ArrayList<>();
            for (ChatRoomDO room : pendingRooms) {
                try {
                    log.info("🏠 处理聊天室: ID={}, RoomCode={}, UserId={}", room.getId(), room.getRoomCode(), room.getUserId());

                    Map<String, Object> roomInfo = new HashMap<>();
                    roomInfo.put("roomId", room.getId());
                    roomInfo.put("roomCode", room.getRoomCode());
                    roomInfo.put("userId", room.getUserId());
                    roomInfo.put("status", room.getStatus());
                    roomInfo.put("createTime", room.getCreateTime());
                    roomInfo.put("lastMessageTime", room.getLastMessageTime());

                    // 获取用户信息
                    User user = userService.queryById(room.getUserId());
                    if (user != null) {
                        roomInfo.put("userName", user.getNickName() != null ? user.getNickName() : user.getUserName());
                        roomInfo.put("userAvatar", user.getAvatar());
                        log.info("✅ 用户信息获取成功: UserName={}", roomInfo.get("userName"));
                    } else {
                        log.warn("⚠️ 用户信息未找到: UserId={}", room.getUserId());
                    }

                    // 获取最新消息
                    ChatMessageDO latestMessage = chatMessageMapper.selectLatestMessageByRoomId(room.getId());
                    if (latestMessage != null) {
                        roomInfo.put("latestMessage", latestMessage.getContent());
                        roomInfo.put("latestMessageTime", latestMessage.getCreateTime());
                        roomInfo.put("latestMessageType", latestMessage.getMessageType());
                    } else {
                        log.info("ℹ️ 该聊天室暂无消息: RoomId={}", room.getId());
                    }

                    // 获取未读消息数量
                    //log.info("🔢 获取未读消息数量: RoomId={}, UserId={}", room.getId(), room.getUserId());
                    int unreadCount = chatMessageMapper.countUnreadMessages(room.getId(), room.getUserId());
                    roomInfo.put("unreadCount", unreadCount);

                    result.add(roomInfo);


                } catch (Exception e) {
                    log.error("❌ 处理聊天室信息时发生异常: RoomId={}, Error={}", room.getId(), e.getMessage(), e);
                    // 继续处理下一个聊天室，不因为单个聊天室的错误而中断整个流程
                }
            }

            // 按最新消息时间排序
            result.sort((a, b) -> {
                Date timeA = (Date) a.get("latestMessageTime");
                Date timeB = (Date) b.get("latestMessageTime");
                if (timeA == null && timeB == null) return 0;
                if (timeA == null) return 1;
                if (timeB == null) return -1;
                return timeB.compareTo(timeA);
            });

            return result;

        } catch (Exception e) {
            log.error("❌ 获取等待客服的聊天室列表时发生异常", e);
            throw new RuntimeException("获取等待客服的聊天室列表失败: " + e.getMessage(), e);
        }
    }


    @Override
    @Transactional
    public void endChatRoom(Long roomId) {
        ChatRoomDO roomDO = chatRoomMapper.selectById(roomId);
        if (roomDO == null) {
            throw new BizException("聊天室不存在");
        }

        // 如果有分配的客服，减少其聊天数
        if (roomDO.getCustomerServiceId() != null) {
            customerServiceService.decrementChatCount(roomDO.getCustomerServiceId());
        }

        // 更新聊天室状态为关闭
        chatRoomMapper.updateStatus(roomId, ChatRoomStatusEnum.CLOSED.getStatus());

        log.info("聊天室已结束，房间ID: {}", roomId);
    }

    @Override
    public List<Map<String, Object>> getAllChatRooms(Integer page, Integer size) {
        try {
            int offset = (page - 1) * size;
            List<ChatRoomDO> allRoomDOS = chatRoomMapper.queryAllByLimit(offset, size);
            List<Map<String, Object>> result = new ArrayList<>();
            for (ChatRoomDO room : allRoomDOS) {
                try {
                    log.info("🏠 处理聊天室: ID={}, RoomCode={}, UserId={}", room.getId(), room.getRoomCode(), room.getUserId());

                    Map<String, Object> roomInfo = new HashMap<>();
                    roomInfo.put("roomId", room.getId());
                    roomInfo.put("roomCode", room.getRoomCode());
                    roomInfo.put("userId", room.getUserId());
                    roomInfo.put("status", room.getStatus());
                    roomInfo.put("createTime", room.getCreateTime());
                    roomInfo.put("lastMessageTime", room.getLastMessageTime());

                    User user = userService.queryById(room.getUserId());
                    if (user != null) {
                        roomInfo.put("userName", user.getNickName() != null ? user.getNickName() : user.getUserName());
                        roomInfo.put("userAvatar", user.getAvatar());
                        log.info("✅ 用户信息获取成功: UserName={}", roomInfo.get("userName"));
                    } else {
                        log.warn("⚠️ 用户信息未找到: UserId={}", room.getUserId());
                    }

                    ChatMessageDO latestMessage = chatMessageMapper.selectLatestMessageByRoomId(room.getId());
                    if (latestMessage != null) {
                        roomInfo.put("latestMessage", latestMessage.getContent());
                        roomInfo.put("latestMessageTime", latestMessage.getCreateTime());
                        roomInfo.put("latestMessageType", latestMessage.getMessageType());
                    } else {
                        log.info("ℹ️ 该聊天室暂无消息: RoomId={}", room.getId());
                    }

                    int unreadCount = chatMessageMapper.countUnreadMessages(room.getId(), room.getUserId());
                    roomInfo.put("unreadCount", unreadCount);
                    result.add(roomInfo);

                } catch (Exception e) {
                    log.error("❌ 处理聊天室信息时发生异常: RoomId={}, Error={}", room.getId(), e.getMessage(), e);
                    // 继续处理下一个聊天室，不因为单个聊天室的错误而中断整个流程
                }
            }

            // 按最新消息时间排序
            result.sort((a, b) -> {
                Date timeA = (Date) a.get("latestMessageTime");
                Date timeB = (Date) b.get("latestMessageTime");
                if (timeA == null && timeB == null) return 0;
                if (timeA == null) return 1;
                if (timeB == null) return -1;
                return timeB.compareTo(timeA);
            });
            //log.info("✅ 等待客服的聊天室列表获取完成，共 {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("❌ 获取等待客服的聊天室列表时发生异常", e);
            throw new RuntimeException("获取等待客服的聊天室列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成房间编码
     */
    private String generateRoomCode() {
        return "ROOM_" + IdUtil.getSnowflakeNextIdStr();
    }
}
